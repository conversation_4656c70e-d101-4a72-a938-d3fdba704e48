/**
 * 性能测试工具
 * 用于测试和比较渲染引擎的性能优化效果
 */

export class PerformanceTest {
	constructor(viewEngine) {
		this.viewEngine = viewEngine;
		this.testResults = [];
		this.isRunning = false;
	}

	/**
	 * 运行性能测试
	 * @param {number} duration - 测试持续时间（秒）
	 * @param {string} testName - 测试名称
	 */
	async runTest(duration = 10, testName = 'Performance Test') {
		if (this.isRunning) {
			console.warn('性能测试已在运行中');
			return;
		}

		console.log(`开始性能测试: ${testName}`);
		this.isRunning = true;

		const startTime = performance.now();
		const endTime = startTime + duration * 1000;
		const samples = [];

		// 收集性能数据
		const collectData = () => {
			if (performance.now() < endTime && this.isRunning) {
				const stats = this.viewEngine.getPerformanceStats();
				samples.push({
					timestamp: performance.now() - startTime,
					...stats
				});
				requestAnimationFrame(collectData);
			} else {
				this.finishTest(testName, samples, startTime);
			}
		};

		requestAnimationFrame(collectData);
	}

	/**
	 * 完成测试并生成报告
	 */
	finishTest(testName, samples, startTime) {
		this.isRunning = false;
		const duration = (performance.now() - startTime) / 1000;

		if (samples.length === 0) {
			console.warn('没有收集到性能数据');
			return;
		}

		// 计算统计数据
		const fps = samples.map(s => s.fps);
		const frameTime = samples.map(s => s.frameTime);

		const result = {
			testName,
			duration,
			sampleCount: samples.length,
			fps: {
				min: Math.min(...fps),
				max: Math.max(...fps),
				avg: fps.reduce((a, b) => a + b, 0) / fps.length,
				median: this.getMedian(fps)
			},
			frameTime: {
				min: Math.min(...frameTime),
				max: Math.max(...frameTime),
				avg: frameTime.reduce((a, b) => a + b, 0) / frameTime.length,
				median: this.getMedian(frameTime)
			},
			objectCount: samples[0]?.objectCount || 0,
			materialCount: samples[0]?.materialCount || 0,
			geometryCount: samples[0]?.geometryCount || 0,
			timestamp: new Date().toISOString()
		};

		this.testResults.push(result);
		this.printTestResult(result);
		return result;
	}

	/**
	 * 获取中位数
	 */
	getMedian(arr) {
		const sorted = [...arr].sort((a, b) => a - b);
		const mid = Math.floor(sorted.length / 2);
		return sorted.length % 2 !== 0 ? sorted[mid] : (sorted[mid - 1] + sorted[mid]) / 2;
	}

	/**
	 * 打印测试结果
	 */
	printTestResult(result) {
		console.log(`\n=== ${result.testName} 测试结果 ===`);
		console.log(`测试时长: ${result.duration.toFixed(2)}秒`);
		console.log(`采样数量: ${result.sampleCount}`);
		console.log(`\n帧率 (FPS):`);
		console.log(`  平均: ${result.fps.avg.toFixed(1)}`);
		console.log(`  中位数: ${result.fps.median.toFixed(1)}`);
		console.log(`  最小: ${result.fps.min}`);
		console.log(`  最大: ${result.fps.max}`);
		console.log(`\n帧时间 (ms):`);
		console.log(`  平均: ${result.frameTime.avg.toFixed(2)}`);
		console.log(`  中位数: ${result.frameTime.median.toFixed(2)}`);
		console.log(`  最小: ${result.frameTime.min.toFixed(2)}`);
		console.log(`  最大: ${result.frameTime.max.toFixed(2)}`);
		console.log(`\n场景统计:`);
		console.log(`  对象数量: ${result.objectCount}`);
		console.log(`  材质数量: ${result.materialCount}`);
		console.log(`  几何体数量: ${result.geometryCount}`);
		console.log(`=====================================\n`);
	}

	/**
	 * 比较两个测试结果
	 */
	compareResults(result1, result2) {
		if (!result1 || !result2) {
			console.warn('需要两个有效的测试结果进行比较');
			return;
		}

		console.log(`\n=== 性能对比: ${result1.testName} vs ${result2.testName} ===`);
		
		const fpsImprovement = ((result2.fps.avg - result1.fps.avg) / result1.fps.avg * 100);
		const frameTimeImprovement = ((result1.frameTime.avg - result2.frameTime.avg) / result1.frameTime.avg * 100);

		console.log(`平均FPS变化: ${fpsImprovement > 0 ? '+' : ''}${fpsImprovement.toFixed(1)}%`);
		console.log(`平均帧时间变化: ${frameTimeImprovement > 0 ? '+' : ''}${frameTimeImprovement.toFixed(1)}%`);
		
		if (fpsImprovement > 5) {
			console.log('✅ 性能有显著提升');
		} else if (fpsImprovement < -5) {
			console.log('❌ 性能有所下降');
		} else {
			console.log('➖ 性能变化不明显');
		}
		console.log(`=======================================\n`);
	}

	/**
	 * 停止当前测试
	 */
	stopTest() {
		this.isRunning = false;
	}

	/**
	 * 获取所有测试结果
	 */
	getAllResults() {
		return this.testResults;
	}

	/**
	 * 清除测试结果
	 */
	clearResults() {
		this.testResults = [];
	}

	/**
	 * 导出测试结果为JSON
	 */
	exportResults() {
		return JSON.stringify(this.testResults, null, 2);
	}

	/**
	 * 运行性能模式对比测试
	 */
	async runPerformanceModeComparison() {
		const modes = ['high', 'balanced', 'performance'];
		const results = [];

		for (const mode of modes) {
			console.log(`测试性能模式: ${mode}`);
			this.viewEngine.setPerformanceMode(mode);
			
			// 等待设置生效
			await new Promise(resolve => setTimeout(resolve, 1000));
			
			const result = await new Promise(resolve => {
				this.runTest(5, `${mode} 模式`);
				const checkComplete = () => {
					if (!this.isRunning) {
						resolve(this.testResults[this.testResults.length - 1]);
					} else {
						setTimeout(checkComplete, 100);
					}
				};
				checkComplete();
			});
			
			results.push(result);
		}

		// 比较结果
		console.log('\n=== 性能模式对比结果 ===');
		results.forEach((result, index) => {
			if (index > 0) {
				this.compareResults(results[0], result);
			}
		});

		return results;
	}
}

// 使用示例：
// const performanceTest = new PerformanceTest(viewEngine);
// performanceTest.runTest(10, '优化前测试');
// 
// // 应用优化后
// performanceTest.runTest(10, '优化后测试');
// 
// // 比较结果
// const results = performanceTest.getAllResults();
// if (results.length >= 2) {
//     performanceTest.compareResults(results[0], results[1]);
// }
